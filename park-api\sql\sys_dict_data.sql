/*
 Navicat MySQL Data Transfer

 Source Server         : 远程测试数据库
 Source Server Type    : MySQL
 Source Server Version : 80035
 Source Host           : 127.0.0.1:3306
 Source Schema         : parknew

 Target Server Type    : MySQL
 Target Server Version : 80035
 File Encoding         : 65001

 Date: 01/08/2025 10:06:39
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for sys_dict_data
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_data`;
CREATE TABLE `sys_dict_data`  (
  `dict_code` bigint NOT NULL AUTO_INCREMENT COMMENT '字典编码',
  `dict_sort` int NULL DEFAULT 0 COMMENT '字典排序',
  `dict_label` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典标签',
  `dict_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典键值',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典类型',
  `css_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
  `list_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '表格回显样式',
  `is_default` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'N' COMMENT '是否默认（Y是 N否）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` bigint UNSIGNED NULL DEFAULT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint UNSIGNED NULL DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 285 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '字典数据表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_dict_data
-- ----------------------------
INSERT INTO `sys_dict_data` VALUES (1, 1, '男', '0', 'sys_user_sex', '', '', 'Y', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '性别男');
INSERT INTO `sys_dict_data` VALUES (2, 2, '女', '1', 'sys_user_sex', '', '', 'N', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '性别女');
INSERT INTO `sys_dict_data` VALUES (3, 3, '未知', '2', 'sys_user_sex', '', '', 'N', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '性别未知');
INSERT INTO `sys_dict_data` VALUES (4, 1, '显示', '0', 'sys_show_hide', '', 'primary', 'Y', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '显示菜单');
INSERT INTO `sys_dict_data` VALUES (5, 2, '隐藏', '1', 'sys_show_hide', '', 'danger', 'N', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '隐藏菜单');
INSERT INTO `sys_dict_data` VALUES (6, 1, '正常', '0', 'sys_normal_disable', '', 'primary', 'Y', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '正常状态');
INSERT INTO `sys_dict_data` VALUES (7, 2, '停用', '1', 'sys_normal_disable', '', 'danger', 'N', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '停用状态');
INSERT INTO `sys_dict_data` VALUES (8, 1, '正常', '0', 'sys_job_status', '', 'primary', 'Y', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '正常状态');
INSERT INTO `sys_dict_data` VALUES (9, 2, '暂停', '1', 'sys_job_status', '', 'danger', 'N', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '停用状态');
INSERT INTO `sys_dict_data` VALUES (10, 1, '默认', 'DEFAULT', 'sys_job_group', '', '', 'Y', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '默认分组');
INSERT INTO `sys_dict_data` VALUES (11, 2, '系统', 'SYSTEM', 'sys_job_group', '', '', 'N', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '系统分组');
INSERT INTO `sys_dict_data` VALUES (12, 1, '是', 'Y', 'sys_yes_no', '', 'primary', 'Y', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '系统默认是');
INSERT INTO `sys_dict_data` VALUES (13, 2, '否', 'N', 'sys_yes_no', '', 'danger', 'N', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '系统默认否');
INSERT INTO `sys_dict_data` VALUES (14, 1, '通知', '1', 'sys_notice_type', '', 'warning', 'Y', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '通知');
INSERT INTO `sys_dict_data` VALUES (15, 2, '公告', '2', 'sys_notice_type', '', 'success', 'N', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '公告');
INSERT INTO `sys_dict_data` VALUES (16, 1, '正常', '0', 'sys_notice_status', '', 'primary', 'Y', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '正常状态');
INSERT INTO `sys_dict_data` VALUES (17, 2, '关闭', '1', 'sys_notice_status', '', 'danger', 'N', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '关闭状态');
INSERT INTO `sys_dict_data` VALUES (18, 99, '其他', '0', 'sys_oper_type', '', 'info', 'N', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '其他操作');
INSERT INTO `sys_dict_data` VALUES (19, 1, '新增', '1', 'sys_oper_type', '', 'info', 'N', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '新增操作');
INSERT INTO `sys_dict_data` VALUES (20, 2, '修改', '2', 'sys_oper_type', '', 'info', 'N', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '修改操作');
INSERT INTO `sys_dict_data` VALUES (21, 3, '删除', '3', 'sys_oper_type', '', 'danger', 'N', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '删除操作');
INSERT INTO `sys_dict_data` VALUES (22, 4, '授权', '4', 'sys_oper_type', '', 'primary', 'N', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '授权操作');
INSERT INTO `sys_dict_data` VALUES (23, 5, '导出', '5', 'sys_oper_type', '', 'warning', 'N', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '导出操作');
INSERT INTO `sys_dict_data` VALUES (24, 6, '导入', '6', 'sys_oper_type', '', 'warning', 'N', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '导入操作');
INSERT INTO `sys_dict_data` VALUES (25, 7, '强退', '7', 'sys_oper_type', '', 'danger', 'N', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '强退操作');
INSERT INTO `sys_dict_data` VALUES (26, 8, '生成代码', '8', 'sys_oper_type', '', 'warning', 'N', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '生成操作');
INSERT INTO `sys_dict_data` VALUES (27, 9, '清空数据', '9', 'sys_oper_type', '', 'danger', 'N', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '清空操作');
INSERT INTO `sys_dict_data` VALUES (28, 1, '成功', '0', 'sys_common_status', '', 'primary', 'N', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '正常状态');
INSERT INTO `sys_dict_data` VALUES (29, 2, '失败', '1', 'sys_common_status', '', 'danger', 'N', '0', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:24:07', '停用状态');
INSERT INTO `sys_dict_data` VALUES (100, 1, '生效', '1', 'advert_status', '', 'primary', 'Y', '0', 1, '2025-06-06 13:52:17', 1, '2025-07-02 23:24:07', '广告生效状态');
INSERT INTO `sys_dict_data` VALUES (101, 2, '失效', '2', 'advert_status', '', 'danger', 'N', '0', 1, '2025-06-06 13:52:17', 1, '2025-07-02 23:24:07', '广告失效状态');
INSERT INTO `sys_dict_data` VALUES (102, 1, '正式环境', 'prod', 'manufacturer_env_type', '', 'success', 'Y', '0', 1, '2025-06-11 11:59:12', 1, '2025-07-02 23:24:07', '生产环境API地址');
INSERT INTO `sys_dict_data` VALUES (103, 2, '测试环境', 'test', 'manufacturer_env_type', '', 'warning', 'N', '0', 1, '2025-06-11 11:59:12', 1, '2025-07-02 23:24:07', '测试环境API地址');
INSERT INTO `sys_dict_data` VALUES (104, 1, '营业中', '1', 'warehouse_status', '', 'success', 'Y', '0', 1, '2025-06-18 11:57:19', 1, '2025-07-02 23:24:07', '场库正常营业状态');
INSERT INTO `sys_dict_data` VALUES (105, 2, '未营业', '0', 'warehouse_status', '', 'warning', 'N', '0', 1, '2025-06-18 11:57:19', 1, '2025-06-18 12:01:16', '场库未营业状态');
INSERT INTO `sys_dict_data` VALUES (106, 1, '普通场库', '0', 'warehouse_smart_level', '', 'info', 'Y', '0', 1, '2025-06-18 11:57:34', 1, '2025-07-02 23:24:07', '普通停车场库');
INSERT INTO `sys_dict_data` VALUES (107, 2, '智能场库', '1', 'warehouse_smart_level', '', 'primary', 'N', '0', 1, '2025-06-18 11:57:34', 1, '2025-07-02 23:24:07', '具备基础智能功能的场库');
INSERT INTO `sys_dict_data` VALUES (108, 3, '智慧场库', '2', 'warehouse_smart_level', '', 'success', 'N', '0', 1, '2025-06-18 11:57:34', 1, '2025-07-02 23:24:07', '高度智能化的场库');
INSERT INTO `sys_dict_data` VALUES (109, 1, '否', '0', 'vip_park_sign', '', 'info', 'Y', '0', 1, '2025-06-18 11:57:51', 1, '2025-07-02 23:24:07', '不启用VIP车位管理');
INSERT INTO `sys_dict_data` VALUES (110, 2, '是', '1', 'vip_park_sign', '', 'success', 'N', '0', 1, '2025-06-18 11:57:51', 1, '2025-07-02 23:24:07', '启用VIP车位管理');
INSERT INTO `sys_dict_data` VALUES (111, 1, '待处理', '0', 'exception_order_handle_status', '', 'danger', 'Y', '0', 1, '2025-06-19 15:04:37', 1, '2025-07-02 23:24:07', '异常订单待处理状态');
INSERT INTO `sys_dict_data` VALUES (112, 2, '处理中', '1', 'exception_order_handle_status', '', 'warning', 'N', '0', 1, '2025-06-19 15:04:37', 1, '2025-07-02 23:24:07', '异常订单处理中状态');
INSERT INTO `sys_dict_data` VALUES (113, 3, '已处理', '2', 'exception_order_handle_status', '', 'success', 'N', '0', 1, '2025-06-19 15:04:37', 1, '2025-07-02 23:24:07', '异常订单已处理状态');
INSERT INTO `sys_dict_data` VALUES (114, 4, '已忽略', '3', 'exception_order_handle_status', '', 'info', 'N', '0', 1, '2025-06-19 15:04:37', 1, '2025-07-02 23:24:07', '异常订单已忽略状态');
INSERT INTO `sys_dict_data` VALUES (115, 1, '支付异常', '1', 'exception_order_type', '', 'danger', 'Y', '0', 1, '2025-06-19 15:04:54', 1, '2025-07-02 23:24:07', '支付相关异常');
INSERT INTO `sys_dict_data` VALUES (116, 2, '时间异常', '2', 'exception_order_type', '', 'warning', 'N', '0', 1, '2025-06-19 15:04:54', 1, '2025-07-02 23:24:07', '时间计算异常');
INSERT INTO `sys_dict_data` VALUES (117, 3, '金额异常', '3', 'exception_order_type', '', 'info', 'N', '0', 1, '2025-06-19 15:04:54', 1, '2025-07-02 23:24:07', '金额计算异常');
INSERT INTO `sys_dict_data` VALUES (118, 4, '其他异常', '4', 'exception_order_type', '', 'primary', 'N', '0', 1, '2025-06-19 15:04:54', 1, '2025-07-02 23:24:07', '其他类型异常');
INSERT INTO `sys_dict_data` VALUES (119, 1, '进行中', '0', 'parking_order_status', '', 'warning', 'Y', '0', 1, '2025-06-19 15:05:11', 1, '2025-07-02 23:24:07', '车辆已入场，订单进行中');
INSERT INTO `sys_dict_data` VALUES (120, 2, '已完成', '1', 'parking_order_status', '', 'success', 'N', '0', 1, '2025-06-19 15:05:11', 1, '2025-07-02 23:24:07', '车辆已出场，订单已完成');
INSERT INTO `sys_dict_data` VALUES (121, 3, '已取消', '2', 'parking_order_status', '', 'danger', 'N', '0', 1, '2025-06-19 15:05:11', 1, '2025-07-02 23:24:07', '订单已取消');
INSERT INTO `sys_dict_data` VALUES (122, 4, '异常订单', '3', 'parking_order_status', '', 'info', 'N', '0', 1, '2025-06-19 15:05:11', 1, '2025-07-02 23:24:07', '存在异常的订单');
INSERT INTO `sys_dict_data` VALUES (130, 1, '地上停车场', '1', 'parking_lot_type', '', 'primary', 'Y', '0', 1, '2025-06-20 15:26:51', 1, '2025-07-02 23:24:07', '地上停车场类型');
INSERT INTO `sys_dict_data` VALUES (131, 2, '地下停车场', '2', 'parking_lot_type', '', 'success', 'N', '0', 1, '2025-06-20 15:26:51', 1, '2025-07-02 23:24:07', '地下停车场类型');
INSERT INTO `sys_dict_data` VALUES (132, 1, '正常使用', '1', 'parking_lot_status', '', 'success', 'Y', '0', 1, '2025-06-20 15:27:05', 1, '2025-07-02 23:24:07', '停车场正常使用状态');
INSERT INTO `sys_dict_data` VALUES (133, 2, '暂停使用', '2', 'parking_lot_status', '', 'warning', 'N', '0', 1, '2025-06-20 15:27:05', 1, '2025-07-02 23:24:07', '停车场暂停使用状态');
INSERT INTO `sys_dict_data` VALUES (134, 3, '维护中', '3', 'parking_lot_status', '', 'danger', 'N', '0', 1, '2025-06-20 15:27:05', 1, '2025-07-02 23:24:07', '停车场维护中状态');
INSERT INTO `sys_dict_data` VALUES (140, 1, '启用', '1', 'vip_package_status', '', 'success', 'Y', '0', 1, '2025-06-20 15:29:15', 1, '2025-07-02 23:24:07', '套餐启用状态');
INSERT INTO `sys_dict_data` VALUES (141, 2, '停用', '2', 'vip_package_status', '', 'warning', 'N', '0', 1, '2025-06-20 15:29:15', 1, '2025-07-02 23:24:07', '套餐停用状态');
INSERT INTO `sys_dict_data` VALUES (157, 1, '正常', '1', 'operator_status', '', 'success', 'Y', '0', 1, '2025-06-20 15:37:41', 1, '2025-07-02 23:24:07', '运营商正常状态');
INSERT INTO `sys_dict_data` VALUES (158, 2, '停用', '0', 'operator_status', '', 'danger', 'N', '0', 1, '2025-06-20 15:37:41', 1, '2025-07-02 23:24:07', '运营商停用状态');
INSERT INTO `sys_dict_data` VALUES (163, 2, '微信', '2', 'pay_method', '', 'success', 'Y', '0', 1, '2025-06-20 15:42:20', 1, '2025-07-30 00:08:26', '微信支付方式');
INSERT INTO `sys_dict_data` VALUES (167, 1, '支付宝', '1', 'pay_method', '', 'success', 'N', '0', 1, '2025-06-26 09:27:35', 1, '2025-07-30 00:08:33', '支付宝支付方式');
INSERT INTO `sys_dict_data` VALUES (168, 1, '低', '1', 'priority_level', '', 'info', 'N', '0', 1, '2025-06-26 09:30:45', 1, '2025-07-02 23:24:07', '低优先级');
INSERT INTO `sys_dict_data` VALUES (169, 2, '中', '2', 'priority_level', '', 'warning', 'N', '0', 1, '2025-06-26 09:30:45', 1, '2025-07-02 23:24:07', '中优先级');
INSERT INTO `sys_dict_data` VALUES (170, 3, '高', '3', 'priority_level', '', 'danger', 'N', '0', 1, '2025-06-26 09:30:45', 1, '2025-07-02 23:24:07', '高优先级');
INSERT INTO `sys_dict_data` VALUES (171, 4, '紧急', '4', 'priority_level', '', 'danger', 'N', '0', 1, '2025-06-26 09:30:45', 1, '2025-07-02 23:24:07', '紧急优先级');
INSERT INTO `sys_dict_data` VALUES (172, 1, '系统自动检测', '1', 'source_type', '', 'primary', 'N', '0', 1, '2025-06-26 09:31:00', 1, '2025-07-02 23:24:07', '系统自动检测');
INSERT INTO `sys_dict_data` VALUES (173, 2, '人工上报', '2', 'source_type', '', 'warning', 'N', '0', 1, '2025-06-26 09:31:00', 1, '2025-07-02 23:24:07', '人工上报');
INSERT INTO `sys_dict_data` VALUES (174, 3, '用户投诉', '3', 'source_type', '', 'danger', 'N', '0', 1, '2025-06-26 09:31:00', 1, '2025-07-02 23:24:07', '用户投诉');
INSERT INTO `sys_dict_data` VALUES (177, 1, '普通车牌', '1', 'plate_type', '', 'primary', 'N', '0', 1, '2025-06-26 10:29:04', 1, '2025-07-02 23:24:07', '7位普通车牌');
INSERT INTO `sys_dict_data` VALUES (178, 2, '新能源车牌', '2', 'plate_type', '', 'success', 'N', '0', 1, '2025-06-26 10:29:04', 1, '2025-07-02 23:24:07', '8位新能源车牌');
INSERT INTO `sys_dict_data` VALUES (186, 1, '一天', '1', 'vip_package_type', '', 'primary', 'N', '0', 1, '2025-07-01 16:00:33', 1, '2025-07-02 23:24:07', '1天套餐');
INSERT INTO `sys_dict_data` VALUES (187, 2, '三天', '3', 'vip_package_type', '', 'primary', 'N', '0', 1, '2025-07-01 16:00:33', 1, '2025-07-02 23:24:07', '3天套餐');
INSERT INTO `sys_dict_data` VALUES (188, 3, '五天', '5', 'vip_package_type', '', 'primary', 'N', '0', 1, '2025-07-01 16:00:33', 1, '2025-07-02 23:24:07', '5天套餐');
INSERT INTO `sys_dict_data` VALUES (189, 4, '七天', '7', 'vip_package_type', '', 'success', 'N', '0', 1, '2025-07-01 16:00:33', 1, '2025-07-02 23:24:07', '7天套餐');
INSERT INTO `sys_dict_data` VALUES (190, 5, '十天', '10', 'vip_package_type', '', 'success', 'N', '0', 1, '2025-07-01 16:00:33', 1, '2025-07-02 23:24:07', '10天套餐');
INSERT INTO `sys_dict_data` VALUES (191, 6, '十五天', '15', 'vip_package_type', '', 'success', 'N', '0', 1, '2025-07-01 16:00:33', 1, '2025-07-02 23:24:07', '15天套餐');
INSERT INTO `sys_dict_data` VALUES (192, 7, '包月', '30', 'vip_package_type', '', 'info', 'Y', '0', 1, '2025-07-01 16:00:33', 1, '2025-07-02 23:24:07', '30天套餐');
INSERT INTO `sys_dict_data` VALUES (193, 8, '两个月', '60', 'vip_package_type', '', 'info', 'N', '0', 1, '2025-07-01 16:00:33', 1, '2025-07-02 23:24:07', '60天套餐');
INSERT INTO `sys_dict_data` VALUES (194, 9, '包季', '90', 'vip_package_type', '', 'warning', 'N', '0', 1, '2025-07-01 16:00:33', 1, '2025-07-02 23:24:07', '90天套餐');
INSERT INTO `sys_dict_data` VALUES (195, 10, '半年', '180', 'vip_package_type', '', 'warning', 'N', '0', 1, '2025-07-01 16:00:33', 1, '2025-07-02 23:24:07', '180天套餐');
INSERT INTO `sys_dict_data` VALUES (196, 11, '九个月', '270', 'vip_package_type', '', 'warning', 'N', '0', 1, '2025-07-01 16:00:33', 1, '2025-07-02 23:24:07', '270天套餐');
INSERT INTO `sys_dict_data` VALUES (197, 12, '包年', '365', 'vip_package_type', '', 'danger', 'N', '0', 1, '2025-07-01 16:00:33', 1, '2025-07-02 23:24:07', '365天套餐');
INSERT INTO `sys_dict_data` VALUES (199, 2, '集团客户', '1', 'vip_member_type', '', 'info', 'Y', '0', 1, '2025-07-02 09:15:46', 1, '2025-07-03 01:04:49', '集团客户');
INSERT INTO `sys_dict_data` VALUES (200, 3, 'VIP客户', '2', 'vip_member_type', '', 'primary', 'N', '0', 1, '2025-07-02 09:15:46', 1, '2025-07-03 00:37:52', 'VIP客户');
INSERT INTO `sys_dict_data` VALUES (201, 4, '团购会员', '3', 'vip_member_type', '', 'success', 'N', '0', 1, '2025-07-02 09:15:46', 1, '2025-07-03 00:38:04', '团购会员');
INSERT INTO `sys_dict_data` VALUES (202, 1, '普通会员', '0', 'vip_member_type', '', 'warning', 'N', '0', 1, '2025-07-02 09:15:46', 1, '2025-07-03 00:37:19', '普通会员');
INSERT INTO `sys_dict_data` VALUES (203, 1, '正常', '0', 'wx_user_status', '', 'success', 'Y', '0', 1, '2025-07-04 10:18:44', NULL, '2025-07-04 10:18:44', '正常状态');
INSERT INTO `sys_dict_data` VALUES (204, 2, '停用', '1', 'wx_user_status', '', 'danger', 'N', '0', 1, '2025-07-04 10:18:44', NULL, '2025-07-04 10:18:44', '停用状态');
INSERT INTO `sys_dict_data` VALUES (205, 1, '普通用户', '0', 'wx_user_type', '', 'primary', 'Y', '0', 1, '2025-07-04 10:18:57', NULL, '2025-07-04 10:18:57', '普通用户');
INSERT INTO `sys_dict_data` VALUES (206, 2, '集团客户', '1', 'wx_user_type', '', 'warning', 'N', '0', 1, '2025-07-04 10:18:57', NULL, '2025-07-04 10:18:57', '集团客户');
INSERT INTO `sys_dict_data` VALUES (207, 3, 'VIP客户', '2', 'wx_user_type', '', 'success', 'N', '0', 1, '2025-07-04 10:18:57', NULL, '2025-07-04 10:18:57', 'VIP客户');
INSERT INTO `sys_dict_data` VALUES (208, 1, '道闸控制', '1', 'gate_operation_type', NULL, NULL, 'N', '0', 1, '2025-07-11 08:16:37', NULL, '2025-07-11 08:16:37', 'GATE_CONTROL');
INSERT INTO `sys_dict_data` VALUES (209, 2, '无牌车进场', '2', 'gate_operation_type', NULL, NULL, 'N', '0', 1, '2025-07-11 08:16:37', NULL, '2025-07-11 08:16:37', 'NO_PLATE_ENTRY');
INSERT INTO `sys_dict_data` VALUES (210, 3, '车辆登记', '3', 'gate_operation_type', NULL, NULL, 'N', '0', 1, '2025-07-11 08:16:37', NULL, '2025-07-11 08:16:37', 'VEHICLE_REGISTRATION');
INSERT INTO `sys_dict_data` VALUES (211, 4, '支付处理', '4', 'gate_operation_type', NULL, NULL, 'N', '0', 1, '2025-07-11 08:16:37', NULL, '2025-07-11 08:16:37', 'PAYMENT_PROCESSING');
INSERT INTO `sys_dict_data` VALUES (212, 5, '车辆删除', '5', 'gate_operation_type', NULL, NULL, 'N', '0', 1, '2025-07-11 08:16:37', NULL, '2025-07-11 08:16:37', 'VEHICLE_DELETION');
INSERT INTO `sys_dict_data` VALUES (213, 6, '健康检查', '6', 'gate_operation_type', NULL, NULL, 'N', '0', 1, '2025-07-11 08:16:37', NULL, '2025-07-11 08:16:37', 'HEALTH_CHECK');
INSERT INTO `sys_dict_data` VALUES (214, 7, '黑名单管理', '7', 'gate_operation_type', NULL, NULL, 'N', '0', 1, '2025-07-11 08:16:37', NULL, '2025-07-11 08:16:37', 'BLACKLIST_MANAGEMENT');
INSERT INTO `sys_dict_data` VALUES (215, 8, '白名单管理', '8', 'gate_operation_type', NULL, NULL, 'N', '0', 1, '2025-07-11 08:16:37', NULL, '2025-07-11 08:16:37', 'WHITELIST_MANAGEMENT');
INSERT INTO `sys_dict_data` VALUES (216, 1, '司卓道闸', '1', 'gate_vendor', '', 'primary', 'N', '0', 1, '2025-07-11 08:16:54', NULL, '2025-07-11 08:16:54', 'SIZHUO');
INSERT INTO `sys_dict_data` VALUES (217, 2, '稳畅道闸', '2', 'gate_vendor', '', 'success', 'N', '0', 1, '2025-07-11 08:16:54', NULL, '2025-07-11 08:16:54', 'WENCHANG');
INSERT INTO `sys_dict_data` VALUES (218, 3, '捷顺道闸', '3', 'gate_vendor', '', 'warning', 'N', '0', 1, '2025-07-11 08:16:54', NULL, '2025-07-11 08:16:54', 'JIESHUN');
INSERT INTO `sys_dict_data` VALUES (219, 4, '泓音道闸', '4', 'gate_vendor', '', 'info', 'N', '0', 1, '2025-07-11 08:16:54', NULL, '2025-07-11 08:16:54', 'HONGYIN');
INSERT INTO `sys_dict_data` VALUES (220, 5, '欣创园道闸', '5', 'gate_vendor', '', 'danger', 'N', '0', 1, '2025-07-11 08:16:54', NULL, '2025-07-11 08:16:54', 'XINCHUANGYUAN');
INSERT INTO `sys_dict_data` VALUES (221, 6, '立方道闸', '6', 'gate_vendor', '', 'default', 'N', '0', 1, '2025-07-11 08:16:54', NULL, '2025-07-11 08:16:54', 'LIFANG');
INSERT INTO `sys_dict_data` VALUES (222, 1, '司卓-道闸控制', '/transmit/gate/control', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:11', NULL, '2025-07-11 08:17:11', '1_1');
INSERT INTO `sys_dict_data` VALUES (223, 2, '司卓-无牌车进场', '/transmit/noplate/entry', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:11', NULL, '2025-07-11 08:17:11', '1_2');
INSERT INTO `sys_dict_data` VALUES (224, 3, '司卓-车辆登记', '/transmit/vehicle/save', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:11', NULL, '2025-07-11 08:17:11', '1_3');
INSERT INTO `sys_dict_data` VALUES (225, 4, '司卓-支付处理', '/transmit/pay/notice', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:11', NULL, '2025-07-11 08:17:11', '1_4');
INSERT INTO `sys_dict_data` VALUES (226, 5, '司卓-车辆删除', '/transmit/vehicle/delete', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:11', NULL, '2025-07-11 08:17:11', '1_5');
INSERT INTO `sys_dict_data` VALUES (227, 6, '司卓-健康检查', '/health', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:11', NULL, '2025-07-11 08:17:11', '1_6');
INSERT INTO `sys_dict_data` VALUES (228, 7, '司卓-黑名单管理', '/transmit/blacklist/manage', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:11', NULL, '2025-07-11 08:17:11', '1_7');
INSERT INTO `sys_dict_data` VALUES (229, 8, '司卓-白名单管理', '/transmit/whitelist/manage', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:11', NULL, '2025-07-11 08:17:11', '1_8');
INSERT INTO `sys_dict_data` VALUES (230, 11, '稳畅-道闸控制', '/gate/control', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:21', NULL, '2025-07-11 08:17:21', '2_1');
INSERT INTO `sys_dict_data` VALUES (231, 12, '稳畅-无牌车进场', '/vehicle/noplate', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:21', NULL, '2025-07-11 08:17:21', '2_2');
INSERT INTO `sys_dict_data` VALUES (232, 13, '稳畅-车辆登记', '/vehicle/register', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:21', NULL, '2025-07-11 08:17:21', '2_3');
INSERT INTO `sys_dict_data` VALUES (233, 14, '稳畅-支付处理', '/payment/process', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:21', NULL, '2025-07-11 08:17:21', '2_4');
INSERT INTO `sys_dict_data` VALUES (234, 15, '稳畅-车辆删除', '/vehicle/delete', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:21', NULL, '2025-07-11 08:17:21', '2_5');
INSERT INTO `sys_dict_data` VALUES (235, 16, '稳畅-健康检查', '/health/check', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:21', NULL, '2025-07-11 08:17:21', '2_6');
INSERT INTO `sys_dict_data` VALUES (236, 17, '稳畅-黑名单管理', '/blacklist/manage', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:21', NULL, '2025-07-11 08:17:21', '2_7');
INSERT INTO `sys_dict_data` VALUES (237, 18, '稳畅-白名单管理', '/whitelist/manage', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:21', NULL, '2025-07-11 08:17:21', '2_8');
INSERT INTO `sys_dict_data` VALUES (238, 21, '捷顺-道闸控制', '/api/gate/control', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:30', NULL, '2025-07-11 08:17:30', '3_1');
INSERT INTO `sys_dict_data` VALUES (239, 22, '捷顺-无牌车进场', '/api/vehicle/noplate/entry', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:30', NULL, '2025-07-11 08:17:30', '3_2');
INSERT INTO `sys_dict_data` VALUES (240, 23, '捷顺-车辆登记', '/api/vehicle/register', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:30', NULL, '2025-07-11 08:17:30', '3_3');
INSERT INTO `sys_dict_data` VALUES (241, 24, '捷顺-支付处理', '/api/payment/notify', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:30', NULL, '2025-07-11 08:17:30', '3_4');
INSERT INTO `sys_dict_data` VALUES (242, 25, '捷顺-车辆删除', '/api/vehicle/remove', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:30', NULL, '2025-07-11 08:17:30', '3_5');
INSERT INTO `sys_dict_data` VALUES (243, 26, '捷顺-健康检查', '/api/health', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:30', NULL, '2025-07-11 08:17:30', '3_6');
INSERT INTO `sys_dict_data` VALUES (244, 27, '捷顺-黑名单管理', '/api/blacklist/manage', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:30', NULL, '2025-07-11 08:17:30', '3_7');
INSERT INTO `sys_dict_data` VALUES (245, 28, '捷顺-白名单管理', '/api/whitelist/manage', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:30', NULL, '2025-07-11 08:17:30', '3_8');
INSERT INTO `sys_dict_data` VALUES (246, 31, '泓音-道闸控制', '/parking/gate/control', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:41', NULL, '2025-07-11 08:17:41', '4_1');
INSERT INTO `sys_dict_data` VALUES (247, 32, '泓音-无牌车进场', '/parking/noplate/entry', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:41', NULL, '2025-07-11 08:17:41', '4_2');
INSERT INTO `sys_dict_data` VALUES (248, 33, '泓音-车辆登记', '/parking/vehicle/save', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:41', NULL, '2025-07-11 08:17:41', '4_3');
INSERT INTO `sys_dict_data` VALUES (249, 34, '泓音-支付处理', '/parking/payment/notify', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:41', NULL, '2025-07-11 08:17:41', '4_4');
INSERT INTO `sys_dict_data` VALUES (250, 35, '泓音-车辆删除', '/parking/vehicle/delete', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:41', NULL, '2025-07-11 08:17:41', '4_5');
INSERT INTO `sys_dict_data` VALUES (251, 36, '泓音-健康检查', '/parking/health', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:41', NULL, '2025-07-11 08:17:41', '4_6');
INSERT INTO `sys_dict_data` VALUES (252, 37, '泓音-黑名单管理', '/parking/blacklist/manage', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:41', NULL, '2025-07-11 08:17:41', '4_7');
INSERT INTO `sys_dict_data` VALUES (253, 38, '泓音-白名单管理', '/parking/whitelist/manage', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:41', NULL, '2025-07-11 08:17:41', '4_8');
INSERT INTO `sys_dict_data` VALUES (254, 41, '欣创园-道闸控制', '/xcy/gate/control', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:52', NULL, '2025-07-11 08:17:52', '5_1');
INSERT INTO `sys_dict_data` VALUES (255, 42, '欣创园-无牌车进场', '/xcy/noplate/entry', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:52', NULL, '2025-07-11 08:17:52', '5_2');
INSERT INTO `sys_dict_data` VALUES (256, 43, '欣创园-车辆登记', '/xcy/vehicle/register', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:52', NULL, '2025-07-11 08:17:52', '5_3');
INSERT INTO `sys_dict_data` VALUES (257, 44, '欣创园-支付处理', '/xcy/payment/process', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:52', NULL, '2025-07-11 08:17:52', '5_4');
INSERT INTO `sys_dict_data` VALUES (258, 45, '欣创园-车辆删除', '/xcy/vehicle/delete', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:52', NULL, '2025-07-11 08:17:52', '5_5');
INSERT INTO `sys_dict_data` VALUES (259, 46, '欣创园-健康检查', '/xcy/health', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:52', NULL, '2025-07-11 08:17:52', '5_6');
INSERT INTO `sys_dict_data` VALUES (260, 47, '欣创园-黑名单管理', '/xcy/blacklist/manage', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:52', NULL, '2025-07-11 08:17:52', '5_7');
INSERT INTO `sys_dict_data` VALUES (261, 48, '欣创园-白名单管理', '/xcy/whitelist/manage', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:17:52', NULL, '2025-07-11 08:17:52', '5_8');
INSERT INTO `sys_dict_data` VALUES (262, 51, '立方-道闸控制', '/lifang/gate/control', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:18:02', NULL, '2025-07-11 08:18:02', '6_1');
INSERT INTO `sys_dict_data` VALUES (263, 52, '立方-无牌车进场', '/lifang/noplate/entry', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:18:02', NULL, '2025-07-11 08:18:02', '6_2');
INSERT INTO `sys_dict_data` VALUES (264, 53, '立方-车辆登记', '/lifang/vehicle/register', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:18:02', NULL, '2025-07-11 08:18:02', '6_3');
INSERT INTO `sys_dict_data` VALUES (265, 54, '立方-支付处理', '/lifang/payment/notify', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:18:02', NULL, '2025-07-11 08:18:02', '6_4');
INSERT INTO `sys_dict_data` VALUES (266, 55, '立方-车辆删除', '/lifang/vehicle/delete', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:18:02', NULL, '2025-07-11 08:18:02', '6_5');
INSERT INTO `sys_dict_data` VALUES (267, 56, '立方-健康检查', '/lifang/health', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:18:02', NULL, '2025-07-11 08:18:02', '6_6');
INSERT INTO `sys_dict_data` VALUES (268, 57, '立方-黑名单管理', '/lifang/blacklist/manage', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:18:02', NULL, '2025-07-11 08:18:02', '6_7');
INSERT INTO `sys_dict_data` VALUES (269, 58, '立方-白名单管理', '/lifang/whitelist/manage', 'gate_api_path', NULL, NULL, 'N', '0', 1, '2025-07-11 08:18:02', NULL, '2025-07-11 08:18:02', '6_8');
INSERT INTO `sys_dict_data` VALUES (270, 1, '未识别车牌', '0', 'error_data_code', '', 'danger', 'N', '0', 1, '2025-07-11 16:19:00', NULL, '2025-07-11 16:19:00', '车牌识别失败');
INSERT INTO `sys_dict_data` VALUES (271, 2, '入场时无出场记录', '1', 'error_data_code', '', 'warning', 'N', '0', 1, '2025-07-11 16:19:00', NULL, '2025-07-11 16:19:00', '入场时发现无对应出场记录');
INSERT INTO `sys_dict_data` VALUES (272, 3, '出场时无入场记录', '2', 'error_data_code', '', 'warning', 'N', '0', 1, '2025-07-11 16:19:00', NULL, '2025-07-11 16:19:00', '出场时发现无对应入场记录');
INSERT INTO `sys_dict_data` VALUES (273, 4, '收费异常', '3', 'error_data_code', '', 'danger', 'N', '0', 1, '2025-07-11 16:19:00', NULL, '2025-07-11 16:19:00', '停车费用计算或收取异常');
INSERT INTO `sys_dict_data` VALUES (274, 1, '小程序', '1', 'pay_type', '', 'primary', 'N', '0', 1, '2025-07-19 00:13:17', NULL, '2025-07-19 00:13:17', '小程序支付');
INSERT INTO `sys_dict_data` VALUES (275, 2, 'C扫B', '2', 'pay_type', '', 'success', 'N', '0', 1, '2025-07-19 00:13:17', NULL, '2025-07-19 00:13:17', 'C扫B支付');
INSERT INTO `sys_dict_data` VALUES (276, 3, 'H5', '3', 'pay_type', '', 'info', 'N', '0', 1, '2025-07-19 00:13:17', NULL, '2025-07-19 00:13:17', 'H5支付');
INSERT INTO `sys_dict_data` VALUES (277, 1, '用户服务条款', '0', 'agreement_type', '', 'default', 'N', '0', 1, '2025-07-22 10:01:40', NULL, '2025-07-22 10:01:40', NULL);
INSERT INTO `sys_dict_data` VALUES (278, 2, '隐私政策', '1', 'agreement_type', '', 'default', 'N', '0', 1, '2025-07-22 10:01:40', NULL, '2025-07-22 10:01:40', NULL);
INSERT INTO `sys_dict_data` VALUES (279, 3, '发票抬头协议', '2', 'agreement_type', '', 'default', 'N', '0', 1, '2025-07-22 10:01:40', NULL, '2025-07-22 10:01:40', NULL);
INSERT INTO `sys_dict_data` VALUES (280, 1, '待支付', '1', 'pay_status', '', 'warning', 'N', '0', 1, '2025-07-26 11:29:15', NULL, '2025-07-26 11:29:15', '订单待支付状态');
INSERT INTO `sys_dict_data` VALUES (281, 2, '支付中', '2', 'pay_status', '', 'primary', 'N', '0', 1, '2025-07-26 11:29:15', NULL, '2025-07-26 11:29:15', '订单支付中状态');
INSERT INTO `sys_dict_data` VALUES (282, 3, '已失败', '3', 'pay_status', '', 'info', 'N', '0', 1, '2025-07-26 11:29:15', NULL, '2025-07-26 11:29:15', '订单已失败状态');
INSERT INTO `sys_dict_data` VALUES (283, 4, '已退款', '4', 'pay_status', '', 'danger', 'N', '0', 1, '2025-07-26 11:29:15', NULL, '2025-07-26 11:29:15', '订单已退款状态');
INSERT INTO `sys_dict_data` VALUES (284, 5, '已支付', '5', 'pay_status', '', 'success', 'Y', '0', 1, '2025-07-26 11:29:15', NULL, '2025-07-26 11:34:57', '订单已支付状态');

SET FOREIGN_KEY_CHECKS = 1;
